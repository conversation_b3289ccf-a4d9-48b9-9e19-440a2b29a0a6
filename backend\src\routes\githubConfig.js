import express from 'express';
import { body, validationResult } from 'express-validator';
import { authenticateToken } from '../middleware/auth.js';
import { Octokit } from 'octokit';

const router = express.Router();
import {
  storeGitHubToken,
  validateGitHubToken,
  getRepositories,
  getCommits,
  getRepository,
} from '../services/githubService.js';

// Middleware para tratar erros de validação
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Dados inválidos',
      details: errors.array().map(err => ({
        field: err.path,
        message: err.msg,
      })),
    });
  }
  next();
};

// Validações para configuração do token GitHub
const validarGitHubToken = [
  body('githubToken')
    .notEmpty()
    .withMessage('Token GitHub é obrigatório')
    .isLength({ min: 40 })
    .withMessage('Token GitHub deve ter pelo menos 40 caracteres')
    .matches(/^ghp_[a-zA-Z0-9]{36}$|^github_pat_[a-zA-Z0-9]{82}$/)
    .withMessage('Token GitHub deve ter formato válido (ghp_ ou github_pat_)'),

  handleValidationErrors,
];

/**
 * @route POST /api/github/config
 * @desc Configura o token GitHub do usuário
 * @access Private
 */
router.post(
  '/config',
  authenticateToken,
  validarGitHubToken,
  async (req, res) => {
    try {
      const { githubToken } = req.body;
      const userId = req.user.userId;

      // Validar se o token é válido fazendo uma chamada para a API GitHub
      const isValid = await validateGitHubToken(githubToken);

      if (!isValid) {
        return res.status(400).json({
          error: 'Token GitHub inválido',
          error_en: 'Invalid GitHub token',
        });
      }

      // Armazenar o token
      storeGitHubToken(userId, githubToken);

      res.json({
        success: true,
        message: 'Token GitHub configurado com sucesso',
        message_en: 'GitHub token configured successfully',
      });
    } catch (error) {
      console.error('Erro ao configurar token GitHub:', error);
      res.status(500).json({
        error: 'Erro interno do servidor ao configurar token',
        error_en: 'Internal server error while configuring token',
      });
    }
  }
);

/**
 * @route GET /api/github/config/status
 * @desc Verifica se o usuário tem token GitHub configurado
 * @access Private
 */
router.get('/config/status', authenticateToken, (req, res) => {
  try {
    const userId = req.user.userId;
    const { getGitHubToken } = require('../services/githubService');

    const token = getGitHubToken(userId);
    const hasToken = !!token;

    res.json({
      success: true,
      data: {
        hasGitHubToken: hasToken,
        isConfigured: hasToken,
      },
      message: hasToken
        ? 'Token GitHub configurado'
        : 'Token GitHub não configurado',
      message_en: hasToken
        ? 'GitHub token configured'
        : 'GitHub token not configured',
    });
  } catch (error) {
    console.error('Erro ao verificar status do token GitHub:', error);
    res.status(500).json({
      error: 'Erro interno do servidor',
      error_en: 'Internal server error',
    });
  }
});

/**
 * @route GET /api/github/repositories
 * @desc Busca repositórios do usuário autenticado
 * @access Private
 */
router.get('/repositories', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const githubToken = req.headers['x-github-token'];

    // Verificar se o token GitHub foi fornecido
    if (!githubToken) {
      return res.status(401).json({
        error: 'Token GitHub não fornecido',
        error_en: 'GitHub token not provided',
        details: 'Use o header x-github-token para fornecer o token GitHub'
      });
    }

    // Criar instância do Octokit com o token fornecido
    const octokit = new Octokit({
      auth: githubToken,
    });

    // Buscar repositórios reais do GitHub
    const { data: repos } = await octokit.rest.repos.listForAuthenticatedUser({
      sort: 'updated',
      per_page: 100,
    });

    console.log(`✅ ${repos.length} repositórios reais encontrados`);

    // Formatar dados dos repositórios
    const formattedRepos = repos.map(repo => ({
      id: repo.id,
      name: repo.name,
      full_name: repo.full_name,
      description: repo.description,
      private: repo.private,
      fork: repo.fork,
      language: repo.language,
      stargazers_count: repo.stargazers_count,
      forks_count: repo.forks_count,
      updated_at: repo.updated_at,
      html_url: repo.html_url,
      clone_url: repo.clone_url,
    }));

    res.json({
      success: true,
      repositories: formattedRepos,
      total: formattedRepos.length,
    });
  } catch (error) {
    console.error('❌ Erro ao buscar repositórios:', error.message);

    // Se o erro for de autenticação GitHub
    if (error.status === 401) {
      return res.status(401).json({
        error: 'Token GitHub inválido ou expirado',
        error_en: 'Invalid or expired GitHub token',
        details: 'Verifique se o token GitHub é válido e tem as permissões necessárias'
      });
    }

    res.status(500).json({
      error: 'Erro ao buscar repositórios',
      error_en: 'Error fetching repositories',
      details: error.message,
    });
  }
});

/**
 * @route GET /api/github/repositories/:repoName/commits
 * @desc Busca commits de um repositório específico
 * @access Private
 */
router.get('/repositories/:repoName/commits', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const { repoName } = req.params;
    const { limit = 10 } = req.query;
    const commits = await getCommits(userId, repoName, 'main', parseInt(limit));

    res.json(commits);
  } catch (error) {
    console.error('❌ Erro ao buscar commits:', error.message);

    if (error.message === 'Token GitHub não encontrado') {
      return res.status(401).json({
        error: 'Token GitHub não configurado',
        error_en: 'GitHub token not configured',
      });
    }

    res.status(500).json({
      error: 'Erro ao buscar commits',
      error_en: 'Error fetching commits',
      details: error.message,
    });
  }
});

/**
 * @route GET /api/github/repositories/:owner/:repo/languages
 * @desc Busca linguagens de um repositório específico com percentuais
 * @access Private
 */
router.get('/repositories/:owner/:repo/languages', authenticateToken, async (req, res) => {
  // Adicionar headers CORS para esta rota específica
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-github-token');
  res.header('Access-Control-Allow-Credentials', 'true');
  try {
    const { owner, repo } = req.params;
    const githubToken = req.headers['x-github-token'];

    if (!githubToken) {
      return res.status(400).json({
        error: 'Token GitHub não fornecido',
        error_en: 'GitHub token not provided',
        details: 'Cabeçalho x-github-token é obrigatório',
      });
    }

    const octokit = new Octokit({
      auth: githubToken,
    });
    // Buscar linguagens do repositório
    const { data: languages } = await octokit.rest.repos.listLanguages({
      owner,
      repo,
    });

    // Calcular total de bytes
    const totalBytes = Object.values(languages).reduce((sum, bytes) => sum + bytes, 0);

    // Calcular porcentagens e formatar dados
    const formattedLanguages = Object.entries(languages)
      .map(([language, bytes]) => ({
        name: language,
        bytes,
        percentage: totalBytes > 0 ? ((bytes / totalBytes) * 100).toFixed(1) : '0.0',
      }))
      .sort((a, b) => b.bytes - a.bytes); // Ordenar por bytes (maior primeiro)

    res.json({
      success: true,
      languages: formattedLanguages,
      totalBytes,
    });
  } catch (error) {
    console.error('❌ Erro ao buscar linguagens:', error.message);

    if (error.status === 401) {
      return res.status(401).json({
        error: 'Token GitHub inválido ou expirado',
        error_en: 'Invalid or expired GitHub token',
        details: 'Verifique se o token GitHub é válido e tem as permissões necessárias'
      });
    }

    res.status(500).json({
      error: 'Erro ao buscar linguagens do repositório',
      error_en: 'Error fetching repository languages',
      details: error.message,
    });
  }
});

/**
 * @route GET /api/github/repositories/:owner/:repo/commits-count
 * @desc Busca total de commits de um repositório específico
 * @access Private
 */
router.get('/repositories/:owner/:repo/commits-count', authenticateToken, async (req, res) => {
  // Adicionar headers CORS para esta rota específica
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-github-token');
  res.header('Access-Control-Allow-Credentials', 'true');
  try {
    const { owner, repo } = req.params;
    const githubToken = req.headers['x-github-token'];

    if (!githubToken) {
      return res.status(400).json({
        error: 'Token GitHub não fornecido',
        error_en: 'GitHub token not provided',
        details: 'Cabeçalho x-github-token é obrigatório',
      });
    }

    const octokit = new Octokit({
      auth: githubToken,
    });

    // Buscar informações do repositório para obter a branch padrão
    const { data: repoInfo } = await octokit.rest.repos.get({
      owner,
      repo,
    });

    // Buscar commits da branch padrão (limitado a 100 por página, mas vamos contar todas as páginas)
    let totalCommits = 0;
    let page = 1;
    let hasMore = true;

    while (hasMore && page <= 10) { // Limitar a 10 páginas para evitar timeout
      const { data: commits } = await octokit.rest.repos.listCommits({
        owner,
        repo,
        sha: repoInfo.default_branch,
        per_page: 100,
        page,
      });

      totalCommits += commits.length;
      hasMore = commits.length === 100;
      page++;
    }

    res.json({
      success: true,
      repository: `${owner}/${repo}`,
      totalCommits,
      defaultBranch: repoInfo.default_branch,
    });
  } catch (error) {
    console.error('❌ Erro ao buscar total de commits:', error.message);

    if (error.status === 401) {
      return res.status(401).json({
        error: 'Token GitHub inválido ou expirado',
        error_en: 'Invalid or expired GitHub token',
        details: 'Verifique se o token GitHub é válido e tem as permissões necessárias'
      });
    }

    res.status(500).json({
      error: 'Erro ao buscar total de commits',
      error_en: 'Error fetching commits count',
      details: error.message,
    });
  }
});

export default router;
