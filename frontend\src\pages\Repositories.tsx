import { useState, useEffect } from 'react';
import DashboardLayout from '@/components/dashboard/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
  GitBranch,
  Star,
  GitFork,
  Calendar,
  Search,
  RefreshCw,
  ExternalLink,
  Lock,
  Globe,
  AlertCircle,
  Loader2,
  Code,
  Settings,
  MessageSquare,
  Activity,
  Users,
  Clock,
  X,
  TrendingUp,
  Eye,
  Download,
  Share2,
  Heart,
  Filter
} from 'lucide-react';
import githubService from '@/services/github';
import type { GitHubRepository } from '@/types';
import { toast } from 'sonner';

export default function Repositories() {
  const [repositories, setRepositories] = useState<GitHubRepository[]>([]);
  const [filteredRepos, setFilteredRepos] = useState<GitHubRepository[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  const [filterType, setFilterType] = useState<'all' | 'public' | 'private' | 'fork' | 'source' | 'favorites' | 'stars'>('all');
  const [sortBy, setSortBy] = useState<'recent' | 'stars' | 'forks' | 'alphabetical'>('recent');
  const [selectedRepository, setSelectedRepository] = useState<GitHubRepository | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const [favoriteRepos, setFavoriteRepos] = useState<Set<number>>(new Set());

  const [loadingDetails, setLoadingDetails] = useState<Set<number>>(new Set());
  const [activeFilters, setActiveFilters] = useState<Set<string>>(new Set());
  const [loadingProgress, setLoadingProgress] = useState<{current: number, total: number} | null>(null);
  const [loadingRepos, setLoadingRepos] = useState<Set<number>>(new Set());
  const [retryCount, setRetryCount] = useState(0);



  // Buscar repositórios ao carregar a página
  useEffect(() => {
    fetchRepositories();
  }, []);

  // Filtrar repositórios simples e direto
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredRepos(repositories);
    } else {
      const filtered = repositories.filter(repo =>
        repo.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        repo.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        repo.language?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredRepos(filtered);
    }
  }, [repositories, searchTerm]);

  const fetchRepositories = async (isRetry = false) => {
    try {
      setLoading(true);
      const repos = await githubService.getUserRepositories();
      setRepositories(repos);
      setRetryCount(0); // Reset retry count on success

      // Carregar commits e linguagens em background (sem delay)
      setTimeout(() => {
        loadAllRepositoriesCommits(repos);
      }, 100);

      toast.success(`${repos.length} repositórios carregados com sucesso!`);
    } catch (error: any) {
      console.error('Erro ao buscar repositórios:', error);

      // Tratamento específico para diferentes tipos de erro
      if (error?.response?.status === 429) {
        toast.error('Muitas requisições. Aguarde um momento e tente novamente.');

        // Retry automático após 2 segundos para rate limit
        if (retryCount < 2) {
          setTimeout(() => {
            setRetryCount(prev => prev + 1);
            fetchRepositories(true);
          }, 2000);
          return;
        }
      } else if (error?.code === 'ERR_NETWORK') {
        toast.error('Erro de conexão. Verifique se o backend está rodando.');

        // Retry automático após 1 segundo para erro de rede
        if (retryCount < 1 && !isRetry) {
          setTimeout(() => {
            setRetryCount(prev => prev + 1);
            fetchRepositories(true);
          }, 1000);
          return;
        }
      } else if (error?.message?.includes('CORS')) {
        toast.error('Erro de CORS. Recarregue a página.');
      } else {
        toast.error('Erro ao carregar repositórios. Tente novamente.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    fetchRepositories();
  };

  // Carregar commits e linguagens de forma progressiva
  const loadAllRepositoriesCommits = async (repos: GitHubRepository[]) => {
    const batchSize = 3; // Processar 3 repositórios simultaneamente
    const chunkSize = 10; // Processar 10 repositórios por vez, depois continuar

    // Função para processar um chunk de repositórios
    const processChunk = async (startIndex: number) => {
      const endIndex = Math.min(startIndex + chunkSize, repos.length);
      const chunk = repos.slice(startIndex, endIndex);

      // Filtrar apenas repositórios que precisam de dados
      const reposToProcess = chunk.filter(repo =>
        repo.totalCommits === undefined || repo.languages === undefined
      );

      if (reposToProcess.length === 0) {
        // Se não há repositórios para processar neste chunk, continuar para o próximo
        if (endIndex < repos.length) {
          setTimeout(() => processChunk(endIndex), 500);
        }
        return;
      }

      console.log(`Carregando repositórios ${startIndex + 1}-${endIndex} de ${repos.length}`);

      // Atualizar progresso
      setLoadingProgress({ current: endIndex, total: repos.length });

      // Processar em batches menores dentro do chunk
      for (let i = 0; i < reposToProcess.length; i += batchSize) {
        const batch = reposToProcess.slice(i, i + batchSize);

        // Marcar repositórios como loading
        batch.forEach(repo => {
          setLoadingRepos(prev => new Set(prev).add(repo.id));
        });

        // Processar batch em paralelo
        const promises = batch.map(async (repo) => {
          try {
            const [owner, repoName] = repo.full_name.split('/');

            // Carregar dados em paralelo
            const [totalCommits, languages] = await Promise.all([
              repo.totalCommits !== undefined
                ? Promise.resolve(repo.totalCommits)
                : githubService.getRepositoryCommitsCount(owner, repoName),
              repo.languages !== undefined
                ? Promise.resolve(repo.languages)
                : githubService.getRepositoryLanguages(owner, repoName)
            ]);

            // Atualizar repositório na lista
            setRepositories(prev =>
              prev.map(r => r.id === repo.id ? { ...r, totalCommits, languages } : r)
            );

            // Remover do loading
            setLoadingRepos(prev => {
              const newSet = new Set(prev);
              newSet.delete(repo.id);
              return newSet;
            });

            return { ...repo, totalCommits, languages };
          } catch (error) {
            console.error(`Erro ao carregar dados de ${repo.name}:`, error);
            // Remover do loading mesmo com erro
            setLoadingRepos(prev => {
              const newSet = new Set(prev);
              newSet.delete(repo.id);
              return newSet;
            });
            return repo;
          }
        });

        await Promise.all(promises);

        // Pequena pausa entre batches
        if (i + batchSize < reposToProcess.length) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }

      // Continuar para o próximo chunk após uma pausa maior
      if (endIndex < repos.length) {
        setTimeout(() => processChunk(endIndex), 1000);
      } else {
        console.log('✅ Carregamento de todos os repositórios concluído!');
        setLoadingProgress(null); // Limpar progresso quando terminar
      }
    };

    // Inicializar progresso e começar processamento
    if (repos.length > 0) {
      setLoadingProgress({ current: 0, total: repos.length });
      processChunk(0);
    }
  };

  // Carregar detalhes de um repositório (linguagens e commits)
  const loadRepositoryDetails = async (repo: GitHubRepository) => {
    if (repo.languages && repo.totalCommits !== undefined) {
      return repo; // Já tem os detalhes carregados
    }

    setLoadingDetails(prev => new Set(prev).add(repo.id));

    try {
      const [owner, repoName] = repo.full_name.split('/');

      // Carregar dados em paralelo para velocidade
      const [languages, totalCommits] = await Promise.all([
        githubService.getRepositoryLanguages(owner, repoName),
        githubService.getRepositoryCommitsCount(owner, repoName)
      ]);

      const updatedRepo = {
        ...repo,
        languages,
        totalCommits
      };

      // Atualizar o repositório na lista
      setRepositories(prev =>
        prev.map(r => r.id === repo.id ? updatedRepo : r)
      );

      return updatedRepo;
    } catch (error) {
      console.error('Erro ao carregar detalhes do repositório:', error);
      toast.error('Erro ao carregar detalhes do repositório');
      return repo;
    } finally {
      setLoadingDetails(prev => {
        const newSet = new Set(prev);
        newSet.delete(repo.id);
        return newSet;
      });
    }
  };



  const getLanguageColor = (language: string | null) => {
    const colors: Record<string, string> = {
      'JavaScript': 'bg-yellow-500',
      'TypeScript': 'bg-blue-500',
      'Python': 'bg-green-500',
      'Java': 'bg-red-500',
      'C++': 'bg-purple-500',
      'C#': 'bg-indigo-500',
      'C': 'bg-gray-600',
      'PHP': 'bg-violet-500',
      'Ruby': 'bg-red-600',
      'Go': 'bg-cyan-500',
      'Rust': 'bg-orange-500',
      'Swift': 'bg-orange-600',
      'Kotlin': 'bg-purple-600',
      'HTML': 'bg-orange-400',
      'CSS': 'bg-blue-400',
      'SCSS': 'bg-pink-500',
      'Vue': 'bg-green-400',
      'React': 'bg-cyan-400',
      'Angular': 'bg-red-400',
      'Dart': 'bg-blue-300',
      'Shell': 'bg-gray-700',
      'PowerShell': 'bg-blue-700',
      'Dockerfile': 'bg-blue-800',
      'YAML': 'bg-red-300',
      'JSON': 'bg-yellow-600',
      'XML': 'bg-orange-300',
      'Markdown': 'bg-gray-400',
    };
    return colors[language || ''] || 'bg-gray-500';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // Estatísticas dos repositórios
  const stats = {
    total: repositories.length,
    public: repositories.filter(r => !r.private).length,
    private: repositories.filter(r => r.private).length,
    forks: repositories.filter(r => r.fork).length,
    source: repositories.filter(r => !r.fork).length,
    stars: repositories.reduce((acc, repo) => acc + repo.stargazers_count, 0),
    favorites: favoriteRepos.size,
  };

  // Filtrar e ordenar repositórios
  useEffect(() => {
    let filtered = repositories;

    // Aplicar filtros múltiplos
    activeFilters.forEach(filter => {
      switch (filter) {
        case 'public':
          filtered = filtered.filter(repo => !repo.private);
          break;
        case 'private':
          filtered = filtered.filter(repo => repo.private);
          break;
        case 'fork':
          filtered = filtered.filter(repo => repo.fork);
          break;
        case 'source':
          filtered = filtered.filter(repo => !repo.fork);
          break;
        case 'favorites':
          filtered = filtered.filter(repo => favoriteRepos.has(repo.id));
          break;
        case 'stars':
          filtered = filtered.filter(repo => repo.stargazers_count > 0);
          break;
      }
    });

    // Aplicar filtro por tipo (compatibilidade com sistema antigo)
    if (filterType !== 'all' && !activeFilters.has(filterType)) {
      switch (filterType) {
        case 'public':
          filtered = filtered.filter(repo => !repo.private);
          break;
        case 'private':
          filtered = filtered.filter(repo => repo.private);
          break;
        case 'fork':
          filtered = filtered.filter(repo => repo.fork);
          break;
        case 'source':
          filtered = filtered.filter(repo => !repo.fork);
          break;
        case 'favorites':
          filtered = filtered.filter(repo => favoriteRepos.has(repo.id));
          break;
        case 'stars':
          filtered = filtered.filter(repo => repo.stargazers_count > 0);
          break;
      }
    }

    // Aplicar busca por texto
    if (searchTerm) {
      filtered = filtered.filter(repo =>
        repo.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        repo.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Aplicar ordenação
    switch (sortBy) {
      case 'recent':
        filtered.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());
        break;
      case 'stars':
        filtered.sort((a, b) => b.stargazers_count - a.stargazers_count);
        break;
      case 'forks':
        filtered.sort((a, b) => b.forks_count - a.forks_count);
        break;
      case 'alphabetical':
        filtered.sort((a, b) => a.name.localeCompare(b.name));
        break;
    }

    setFilteredRepos(filtered);
  }, [repositories, filterType, searchTerm, sortBy, favoriteRepos, activeFilters]);

  // Função para alternar filtros múltiplos
  const toggleFilter = (filter: string) => {
    setActiveFilters(prev => {
      const newFilters = new Set(prev);
      if (newFilters.has(filter)) {
        newFilters.delete(filter);
      } else {
        newFilters.add(filter);
      }
      return newFilters;
    });
  };

  // Funções de ação para repositórios
  const handleRepositoryAction = async (action: string, repo: GitHubRepository) => {
    switch (action) {
      case 'manage':
        const repoWithDetails = await loadRepositoryDetails(repo);
        setSelectedRepository(repoWithDetails);
        setIsModalOpen(true);
        break;
      case 'generate-post':
        toast.success(`Gerando post para ${repo.name}...`);
        break;
      case 'view-github':
        window.open(repo.html_url, '_blank');
        break;
      case 'clone':
        navigator.clipboard.writeText(repo.clone_url);
        toast.success('URL do repositório copiada!');
        break;
      case 'analyze':
        toast.success(`Analisando ${repo.name}...`);
        break;
      case 'favorite':
        toggleFavorite(repo.id);
        break;
      case 'share':
        if (navigator.share) {
          navigator.share({
            title: repo.name,
            text: repo.description || `Confira o repositório ${repo.name}`,
            url: repo.html_url
          });
        } else {
          navigator.clipboard.writeText(repo.html_url);
          toast.success('Link copiado para a área de transferência!');
        }
        break;
      default:
        break;
    }
  };



  // Gerenciar favoritos
  const toggleFavorite = (repoId: number) => {
    setFavoriteRepos(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(repoId)) {
        newFavorites.delete(repoId);
        toast.success('Removido dos favoritos');
      } else {
        newFavorites.add(repoId);
        toast.success('Adicionado aos favoritos');
      }
      return newFavorites;
    });
  };







  return (
    <DashboardLayout>
      <div className="p-3 sm:p-4 lg:p-6 space-y-4 sm:space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
          <div className="min-w-0 flex-1">
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent leading-tight">
              Repositórios GitHub
            </h1>
            <p className="text-slate-300 mt-1 sm:mt-2 text-sm sm:text-base">
              Gerencie e selecione seus repositórios para geração de posts
            </p>
          </div>

          <Button
            onClick={handleRefresh}
            disabled={loading}
            size="sm"
            className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 w-full sm:w-auto"
          >
            {loading ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="w-4 h-4 mr-2" />
            )}
            <span className="hidden sm:inline">Atualizar</span>
            <span className="sm:hidden">Sync</span>
          </Button>
        </div>

        {/* Loading Progress Indicator */}
        {loadingProgress && (
          <div className="bg-slate-800/50 backdrop-blur-xl border border-slate-700/50 rounded-lg p-3 mb-4">
            <div className="flex items-center justify-between text-sm text-slate-300 mb-2">
              <span>Carregando dados dos repositórios...</span>
              <span>{loadingProgress.current} de {loadingProgress.total}</span>
            </div>
            <div className="w-full bg-slate-700/50 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(loadingProgress.current / loadingProgress.total) * 100}%` }}
              />
            </div>
          </div>
        )}

        {/* Advanced Search and Filters */}
        <Card className="group relative overflow-hidden bg-gradient-to-r from-slate-800/60 via-slate-800/40 to-slate-800/60 backdrop-blur-xl border border-slate-700/50 shadow-xl hover:shadow-2xl transition-all duration-500">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-600/5 via-transparent to-blue-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
          <CardContent className="relative p-4 sm:p-6">
            <div className="flex flex-col lg:flex-row gap-4 lg:gap-6">
              {/* Search Input */}
              <div className="flex-1 relative">
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400">
                  <Search className="w-5 h-5" />
                </div>
                <Input
                  id="repository-search"
                  name="repository-search"
                  placeholder="Buscar por nome, descrição ou linguagem..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-12 pr-4 py-3 bg-slate-700/50 border-slate-600/50 text-white placeholder-slate-400 focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 text-base"
                />
                {searchTerm && (
                  <Button
                    onClick={() => setSearchTerm('')}
                    size="sm"
                    variant="ghost"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white p-1 h-auto"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                )}
              </div>

              {/* Sort and Filter Controls */}
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4">
                <div className="flex items-center space-x-3">
                  <label className="text-slate-300 text-sm font-medium whitespace-nowrap">
                    Ordenar:
                  </label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as any)}
                    className="bg-slate-700/50 border border-slate-600/50 rounded-lg px-4 py-2.5 text-white text-sm focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 min-w-[140px]"
                  >
                    <option value="recent">Mais Recentes</option>
                    <option value="stars">Mais Stars</option>
                    <option value="forks">Mais Forks</option>
                    <option value="alphabetical">A-Z</option>
                  </select>
                </div>


              </div>
            </div>

            {/* Active Filters Summary */}
            {(searchTerm || filterType !== 'all' || activeFilters.size > 0) && (
              <div className="mt-4 pt-4 border-t border-slate-700/50">
                <div className="flex flex-wrap items-center gap-2">
                  <span className="text-slate-400 text-sm font-medium">Filtros ativos:</span>
                  {searchTerm && (
                    <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/30 hover:bg-blue-500/20 transition-colors duration-300">
                      <Search className="w-3 h-3 mr-1" />
                      "{searchTerm}"
                    </Badge>
                  )}
                  {filterType !== 'all' && !activeFilters.has(filterType) && (
                    <Badge className="bg-purple-500/10 text-purple-400 border-purple-500/30 hover:bg-purple-500/20 transition-colors duration-300">
                      Tipo: {filterType}
                    </Badge>
                  )}
                  {Array.from(activeFilters).map(filter => (
                    <Badge
                      key={filter}
                      className="bg-green-500/10 text-green-400 border-green-500/30 hover:bg-green-500/20 transition-colors duration-300 cursor-pointer"
                      onClick={() => toggleFilter(filter)}
                    >
                      <Filter className="w-3 h-3 mr-1" />
                      {filter === 'public' ? 'Públicos' :
                        filter === 'private' ? 'Privados' :
                          filter === 'fork' ? 'Forks' :
                            filter === 'source' ? 'Originais' :
                              filter === 'favorites' ? 'Favoritos' :
                                filter === 'stars' ? 'Com Stars' : filter}
                      <X className="w-3 h-3 ml-1" />
                    </Badge>
                  ))}
                  <Button
                    onClick={() => {
                      requestAnimationFrame(() => {
                        setSearchTerm('');
                        setFilterType('all');
                        setActiveFilters(new Set());
                      });
                    }}
                    size="sm"
                    variant="ghost"
                    className="text-slate-400 hover:text-white text-xs ml-2 px-2 py-1 h-auto"
                  >
                    <X className="w-3 h-3 mr-1" />
                    Limpar
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Stats Cards - Interativos como filtros */}
        {!loading && repositories.length > 0 && (
          <div className="grid grid-cols-2 lg:grid-cols-6 gap-3 sm:gap-4">
            {[
              { key: 'all', label: 'Total', count: stats.total, icon: GitBranch, color: 'text-purple-400' },
              { key: 'public', label: 'Públicos', count: stats.public, icon: Globe, color: 'text-green-400' },
              { key: 'private', label: 'Privados', count: stats.private, icon: Lock, color: 'text-yellow-400' },
              { key: 'source', label: 'Originais', count: stats.source, icon: Code, color: 'text-blue-400' },
              { key: 'fork', label: 'Forks', count: stats.forks, icon: GitFork, color: 'text-orange-400' },
              { key: 'favorites', label: 'Favoritos', count: stats.favorites, icon: Heart, color: 'text-red-400' },
              { key: 'stars', label: 'Stars', count: stats.stars, icon: Star, color: 'text-pink-400' },
            ].map(({ key, label, count, icon: Icon, color }) => {
              const isSelected = key === 'all'
                ? (filterType === 'all' && activeFilters.size === 0)
                : activeFilters.has(key);
              const isHovered = false;

              return (
                <Card
                  key={key}
                  className={`
                    relative overflow-hidden transition-all duration-500 transform cursor-pointer backdrop-blur-xl
                    ${isSelected || isHovered
                      ? 'scale-105 bg-slate-800/80 border-blue-500/50 shadow-2xl shadow-blue-500/20'
                      : 'bg-slate-800/50 border-slate-700/50 shadow-xl'
                    }
                  `}
                  onClick={() => {
                    requestAnimationFrame(() => {
                      if (key === 'all') {
                        setFilterType('all');
                        setActiveFilters(new Set());
                      } else {
                        toggleFilter(key);
                      }
                    });
                  }}

                >
                  {/* Efeito de brilho */}
                  <div className={`
                    absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10
                    transition-opacity duration-500 ${isHovered || isSelected ? 'opacity-100' : 'opacity-0'}
                  `} />

                  <CardContent className="p-3 sm:p-4 relative z-10">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className={`
                          text-xs sm:text-sm transition-colors duration-300
                          ${isHovered || isSelected ? 'text-blue-300' : 'text-slate-400'}
                        `}>
                          {label}
                        </p>
                        <p className={`
                          text-xl sm:text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400
                          bg-clip-text text-transparent transition-all duration-300
                          ${isHovered || isSelected ? 'scale-110' : ''}
                        `}>
                          {count}
                        </p>
                      </div>
                      <div className="relative">
                        <Icon className={`
                          w-6 h-6 sm:w-8 sm:h-8 transition-all duration-300
                          ${isHovered || isSelected ? 'text-blue-400 scale-110' : color}
                        `} />
                        {(isHovered || isSelected) && (
                          <div className="absolute inset-0 w-6 h-6 sm:w-8 sm:h-8 bg-blue-400 rounded-full animate-ping opacity-30" />
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}



        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <Loader2 className="w-8 h-8 animate-spin text-purple-400 mx-auto mb-4" />
              <p className="text-slate-300">Carregando repositórios...</p>
            </div>
          </div>
        )}

        {/* Results Summary */}
        {!loading && repositories.length > 0 && (
          <div className="flex items-center justify-between text-sm text-slate-400 mb-4">
            <div className="flex items-center space-x-4">
              <span>
                Mostrando <span className="text-white font-medium">{filteredRepos.length}</span> de{' '}
                <span className="text-white font-medium">{repositories.length}</span> repositórios
              </span>
              {filteredRepos.length !== repositories.length && (
                <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/30">
                  Filtrado
                </Badge>
              )}
            </div>
            {filteredRepos.length > 0 && (
              <div className="text-slate-500">
                Ordenado por: {sortBy === 'recent' ? 'Mais Recentes' : sortBy === 'stars' ? 'Mais Stars' : sortBy === 'forks' ? 'Mais Forks' : 'A-Z'}
              </div>
            )}
          </div>
        )}

        {/* Repositories Grid */}
        {!loading && filteredRepos.length > 0 && (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6">
            {filteredRepos.map((repo) => (
              <Card
                key={repo.id}
                className="group relative overflow-hidden bg-slate-800/50 backdrop-blur-xl border-slate-700/50 shadow-xl hover:shadow-2xl hover:shadow-blue-500/20 hover:border-blue-500/50 hover:scale-[1.02] transition-all duration-500 cursor-pointer"

              >
                {/* Loading Overlay */}
                {loadingRepos.has(repo.id) && (
                  <div className="absolute inset-0 bg-slate-900/80 backdrop-blur-sm z-20 flex items-center justify-center">
                    <div className="flex flex-col items-center space-y-2">
                      <div className="w-8 h-8 border-2 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
                      <span className="text-xs text-slate-300">Carregando...</span>
                    </div>
                  </div>
                )}

                {/* Animated Background Overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 via-transparent to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />

                <CardHeader className="pb-3 relative z-10">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-2">
                        <GitBranch className="w-4 h-4 text-blue-400 flex-shrink-0 group-hover:text-blue-300 group-hover:drop-shadow-[0_0_8px_rgba(59,130,246,0.6)] transition-all duration-300" />
                        <CardTitle className="text-white text-base sm:text-lg truncate group-hover:text-blue-100 transition-colors duration-300">
                          {repo.name}
                        </CardTitle>
                        {repo.private && (
                          <Lock className="w-3 h-3 text-yellow-400 flex-shrink-0 group-hover:text-yellow-300 group-hover:drop-shadow-[0_0_6px_rgba(251,191,36,0.8)] transition-all duration-300" />
                        )}
                        {favoriteRepos.has(repo.id) && (
                          <Heart className="w-3 h-3 text-red-400 fill-current flex-shrink-0 group-hover:text-red-300 group-hover:drop-shadow-[0_0_6px_rgba(248,113,113,0.8)] group-hover:scale-110 transition-all duration-300" />
                        )}
                      </div>
                      <CardDescription className="text-slate-300 text-xs sm:text-sm line-clamp-2">
                        {repo.description || 'Sem descrição disponível'}
                      </CardDescription>
                    </div>

                    {/* Quick Stats Badge */}
                    <div className="flex flex-col items-end space-y-1 ml-2">
                      {repo.stargazers_count > 0 && (
                        <Badge className="bg-yellow-500/10 text-yellow-400 border-yellow-500/30 text-xs px-2 py-0.5">
                          <Star className="w-3 h-3 mr-1" />
                          {repo.stargazers_count}
                        </Badge>
                      )}
                      {repo.language && (
                        <Badge className="bg-slate-700/50 text-slate-300 border-slate-600/50 text-xs px-2 py-0.5">
                          <div className={`w-2 h-2 rounded-full mr-1 ${getLanguageColor(repo.language)}`} />
                          {repo.language}
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="pt-0 relative z-10">
                  {/* Languages */}
                  <div className="mb-3">
                    {repo.languages && repo.languages.length > 0 ? (
                      <div className="space-y-2">
                        <div className="flex flex-wrap gap-1">
                          {repo.languages.slice(0, 5).map((lang) => (
                            <Badge
                              key={lang.name}
                              variant="outline"
                              className="text-xs text-slate-300 border-slate-500/30 bg-slate-700/30 group-hover:border-slate-400/50 group-hover:bg-slate-600/40 group-hover:text-slate-200 transition-all duration-300"
                            >
                              <div className={`w-2 h-2 rounded-full mr-1 ${getLanguageColor(lang.name)}`} />
                              {lang.name}
                            </Badge>
                          ))}
                          {repo.languages.length > 5 && (
                            <Badge variant="outline" className="text-xs text-slate-400 border-slate-500/30">
                              +{repo.languages.length - 5} mais
                            </Badge>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <div className={`w-3 h-3 rounded-full ${getLanguageColor(repo.language)}`}></div>
                        <span className="text-slate-300 text-xs sm:text-sm">
                          {repo.language || 'N/A'}
                        </span>
                        {repo.language && (
                          <Badge variant="outline" className="text-xs text-slate-400 border-slate-500/30">
                            Principal
                          </Badge>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-4 gap-2 sm:gap-3 mb-4">
                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <GitBranch className="w-3 h-3 sm:w-4 sm:h-4 text-blue-400 group-hover:text-blue-300 group-hover:drop-shadow-[0_0_6px_rgba(59,130,246,0.6)] transition-all duration-300" />
                      </div>
                      <div className="text-xs sm:text-sm font-medium text-white">
                        {loadingDetails.has(repo.id) ? (
                          <div className="w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full animate-spin mx-auto"></div>
                        ) : (
                          repo.totalCommits ?? '--'
                        )}
                      </div>
                      <div className="text-xs text-slate-400">Commits</div>
                    </div>

                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Star className="w-3 h-3 sm:w-4 sm:h-4 text-yellow-400 group-hover:text-yellow-300 group-hover:drop-shadow-[0_0_6px_rgba(251,191,36,0.8)] group-hover:scale-110 transition-all duration-300" />
                      </div>
                      <div className="text-xs sm:text-sm font-medium text-white">{repo.stargazers_count}</div>
                      <div className="text-xs text-slate-400">Stars</div>
                    </div>

                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <GitFork className="w-3 h-3 sm:w-4 sm:h-4 text-green-400 group-hover:text-green-300 group-hover:drop-shadow-[0_0_6px_rgba(34,197,94,0.8)] transition-all duration-300" />
                      </div>
                      <div className="text-xs sm:text-sm font-medium text-white">{repo.forks_count}</div>
                      <div className="text-xs text-slate-400">Forks</div>
                    </div>

                    <div className="text-center">
                      <div className="flex items-center justify-center mb-1">
                        <Calendar className="w-3 h-3 sm:w-4 sm:h-4 text-purple-400 group-hover:text-purple-300 group-hover:drop-shadow-[0_0_6px_rgba(147,51,234,0.8)] transition-all duration-300" />
                      </div>
                      <div className="text-xs sm:text-sm font-medium text-white">
                        {formatDate(repo.updated_at)}
                      </div>
                      <div className="text-xs text-slate-400">Atualizado</div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-between pt-3 border-t border-slate-600/30">
                    <div className="flex items-center space-x-1">
                      {/* Favorite Button */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          requestAnimationFrame(() => {
                            handleRepositoryAction('favorite', repo);
                          });
                        }}
                        className={`p-2 transition-all duration-300 ${favoriteRepos.has(repo.id)
                            ? 'text-red-400 hover:text-red-300 hover:bg-red-500/10'
                            : 'text-slate-400 hover:text-red-400 hover:bg-red-500/10'
                          }`}
                        title={favoriteRepos.has(repo.id) ? 'Remover dos favoritos' : 'Adicionar aos favoritos'}
                      >
                        <Heart className={`w-4 h-4 ${favoriteRepos.has(repo.id) ? 'fill-current' : ''}`} />
                      </Button>

                      {/* Share Button */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          requestAnimationFrame(() => {
                            handleRepositoryAction('share', repo);
                          });
                        }}
                        className="text-slate-400 hover:text-blue-400 hover:bg-blue-500/10 p-2 transition-all duration-300"
                        title="Compartilhar"
                      >
                        <Share2 className="w-4 h-4" />
                      </Button>

                      {/* GitHub Link */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          requestAnimationFrame(() => {
                            handleRepositoryAction('view-github', repo);
                          });
                        }}
                        className="text-slate-400 hover:text-white hover:bg-slate-500/10 p-2 transition-all duration-300"
                        title="Ver no GitHub"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </Button>

                      {/* Manage Button */}
                      <Button
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          requestAnimationFrame(() => {
                            handleRepositoryAction('manage', repo);
                          });
                        }}
                        className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white text-xs px-4 py-2 shadow-lg hover:shadow-xl hover:shadow-purple-500/50 hover:scale-105 group-hover:animate-pulse transition-all duration-300"
                      >
                        <Settings className="w-3 h-3 mr-1" />
                        Gerenciar
                      </Button>
                    </div>


                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}



        {/* Empty State */}
        {!loading && filteredRepos.length === 0 && (
          <Card className="group relative overflow-hidden bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-800/60 backdrop-blur-xl border border-slate-700/50 shadow-xl">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-600/5 via-transparent to-blue-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
            <CardContent className="relative text-center py-12 sm:py-16">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-slate-500/20 rounded-full blur-2xl" />
                <GitBranch className="relative w-16 h-16 sm:w-20 sm:h-20 text-slate-400 mx-auto" />
              </div>

              <h3 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-white to-slate-200 bg-clip-text text-transparent mb-3">
                {searchTerm || filterType !== 'all'
                  ? 'Nenhum repositório encontrado'
                  : repositories.length === 0
                    ? 'Nenhum repositório disponível'
                    : 'Filtros muito restritivos'
                }
              </h3>

              <p className="text-slate-400 mb-8 text-base sm:text-lg max-w-md mx-auto leading-relaxed">
                {searchTerm || filterType !== 'all'
                  ? 'Tente ajustar os filtros ou termo de busca para encontrar repositórios'
                  : repositories.length === 0
                    ? 'Conecte sua conta GitHub para ver seus repositórios e começar a gerar posts incríveis'
                    : 'Ajuste os filtros para ver mais repositórios'
                }
              </p>

              <div className="flex flex-col sm:flex-row items-center justify-center gap-3 sm:gap-4">
                {(searchTerm || filterType !== 'all') ? (
                  <Button
                    onClick={() => {
                      requestAnimationFrame(() => {
                        setSearchTerm('');
                        setFilterType('all');
                      });
                    }}
                    className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    <X className="w-4 h-4 mr-2" />
                    Limpar Filtros
                  </Button>
                ) : (
                  <Button
                    onClick={handleRefresh}
                    className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Sincronizar GitHub
                  </Button>
                )}

                <Button
                  variant="outline"
                  onClick={() => window.open('https://thub.com/new', '_blank')}
                  className="border-slate-600/50 text-slate-300 hover:bg-slate-700/50 hover:text-white hover:border-slate-500/70 transition-all duration-300"
                >
                  <GitBranch className="w-4 h-4 mr-2" />
                  Criar Repositório
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Repository Management Modal */}
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogContent className="bg-gradient-to-br from-slate-900/98 via-slate-800/95 to-slate-900/98 backdrop-blur-2xl border border-slate-700/50 shadow-2xl max-h-[90vh] overflow-y-auto p-0">
            {selectedRepository && (
              <div className="relative">
                {/* Background Pattern */}
                <div className="absolute inset-0 bg-gradient-to-br from-purple-600/5 via-transparent to-blue-600/5 pointer-events-none" />

                <div className="relative p-8 space-y-8">
                  {/* Header Section */}
                  <div className="border-b border-slate-700/50 pb-6">
                    <DialogHeader>
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div className="flex items-center space-x-4">
                          <div className="relative">
                            <div className="absolute inset-0 bg-blue-500/20 rounded-full blur-xl" />
                            <GitBranch className="relative w-10 h-10 text-blue-400" />
                          </div>
                          <div>
                            <DialogTitle className="text-white text-2xl sm:text-3xl font-bold bg-gradient-to-r from-white to-slate-200 bg-clip-text text-transparent">
                              {selectedRepository.name}
                            </DialogTitle>
                            <div className="flex items-center space-x-2 mt-2">
                              {selectedRepository.private && (
                                <Badge className="bg-yellow-500/10 text-yellow-400 border-yellow-500/30 hover:bg-yellow-500/20">
                                  <Lock className="w-3 h-3 mr-1" />
                                  Private
                                </Badge>
                              )}
                              {selectedRepository.fork && (
                                <Badge className="bg-orange-500/10 text-orange-400 border-orange-500/30 hover:bg-orange-500/20">
                                  <GitFork className="w-3 h-3 mr-1" />
                                  Fork
                                </Badge>
                              )}
                              {selectedRepository.language && (
                                <Badge className="bg-slate-700/50 text-slate-300 border-slate-600/50">
                                  <div className={`w-2 h-2 rounded-full mr-2 ${getLanguageColor(selectedRepository.language)}`} />
                                  {selectedRepository.language}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>

                        <Button
                          onClick={() => window.open(selectedRepository.html_url, '_blank')}
                          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 focus:ring-2 focus:ring-blue-500/50 focus:outline-none"
                        >
                          <ExternalLink className="w-4 h-4 mr-2" />
                          Ver no GitHub
                        </Button>
                      </div>

                      <DialogDescription className="text-slate-300 text-base mt-4 leading-relaxed">
                        {selectedRepository.description || 'Este repositório não possui uma descrição.'}
                      </DialogDescription>
                    </DialogHeader>
                  </div>

                  {/* Main Content Grid */}
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
                    {/* Stats Card */}
                    <Card className="group relative overflow-hidden bg-gradient-to-br from-slate-800/90 via-slate-800/50 to-slate-900/90 backdrop-blur-xl border border-slate-700/50 shadow-2xl hover:shadow-blue-500/10 transition-all duration-500 min-h-[400px] flex flex-col lg:col-span-2">
                      <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 via-transparent to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                      <CardHeader className="relative">
                        <CardTitle className="text-white flex items-center space-x-3 text-xl">
                          <div className="relative">
                            <div className="absolute inset-0 bg-green-500/20 rounded-full blur-lg" />
                            <Activity className="relative w-6 h-6 text-green-400" />
                          </div>
                          <span className="bg-gradient-to-r from-green-400 to-emerald-300 bg-clip-text text-transparent">
                            Estatísticas
                          </span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="relative space-y-6">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="group/stat text-center p-4 bg-gradient-to-br from-slate-700/50 to-slate-800/50 rounded-xl border border-slate-600/30 hover:border-yellow-500/30 transition-all duration-300">
                            <Star className="w-6 h-6 text-yellow-400 mx-auto mb-2 group-hover/stat:scale-110 transition-transform duration-300" />
                            <div className="text-2xl font-bold bg-gradient-to-r from-yellow-400 to-amber-300 bg-clip-text text-transparent mb-1">
                              {selectedRepository.stargazers_count}
                            </div>
                            <div className="text-xs text-slate-400">Stars</div>
                          </div>
                          <div className="group/stat text-center p-4 bg-gradient-to-br from-slate-700/50 to-slate-800/50 rounded-xl border border-slate-600/30 hover:border-green-500/30 transition-all duration-300">
                            <GitFork className="w-6 h-6 text-green-400 mx-auto mb-2 group-hover/stat:scale-110 transition-transform duration-300" />
                            <div className="text-2xl font-bold bg-gradient-to-r from-green-400 to-emerald-300 bg-clip-text text-transparent mb-1">
                              {selectedRepository.forks_count}
                            </div>
                            <div className="text-xs text-slate-400">Forks</div>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <div className="p-4 bg-gradient-to-r from-slate-700/30 to-slate-800/30 rounded-xl border border-slate-600/20 hover:border-slate-500/40 transition-all duration-300">
                            <div className="flex items-center space-x-3 mb-3">
                              <Code className="w-5 h-5 text-blue-400" />
                              <span className="text-slate-300 font-medium">Linguagens</span>
                            </div>
                            {selectedRepository.languages && selectedRepository.languages.length > 0 ? (
                              <div className="space-y-3">
                                {selectedRepository.languages.map((lang) => (
                                  <div key={lang.name} className="space-y-1">
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center space-x-2">
                                        <div className={`w-3 h-3 rounded-full ${getLanguageColor(lang.name)}`} />
                                        <span className="text-white font-medium">{lang.name}</span>
                                      </div>
                                      <span className="text-slate-300 font-semibold">{lang.percentage}%</span>
                                    </div>
                                    {/* Barra de progresso */}
                                    <div className="w-full bg-slate-700/50 rounded-full h-1.5">
                                      <div
                                        className={`h-1.5 rounded-full ${getLanguageColor(lang.name)} opacity-80`}
                                        style={{ width: `${lang.percentage}%` }}
                                      />
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <div className="flex items-center space-x-2">
                                <div className={`w-3 h-3 rounded-full ${getLanguageColor(selectedRepository.language)}`} />
                                <span className="text-white font-semibold">{selectedRepository.language || 'N/A'}</span>
                              </div>
                            )}
                          </div>

                          <div className="flex items-center justify-between p-4 bg-gradient-to-r from-slate-700/30 to-slate-800/30 rounded-xl border border-slate-600/20 hover:border-slate-500/40 transition-all duration-300">
                            <div className="flex items-center space-x-3">
                              <Calendar className="w-5 h-5 text-purple-400" />
                              <span className="text-slate-300 font-medium">Atualizado</span>
                            </div>
                            <span className="text-white font-semibold">{formatDate(selectedRepository.updated_at)}</span>
                          </div>

                          <div className="flex items-center justify-between p-4 bg-gradient-to-r from-slate-700/30 to-slate-800/30 rounded-xl border border-slate-600/20 hover:border-slate-500/40 transition-all duration-300">
                            <div className="flex items-center space-x-3">
                              <Users className="w-5 h-5 text-cyan-400" />
                              <span className="text-slate-300 font-medium">Commits</span>
                            </div>
                            <span className="text-white font-semibold">
                              {selectedRepository.totalCommits ?? '--'}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Right Column - Actions and Links */}
                    <div className="flex flex-col gap-4">
                      {/* Actions Card */}
                      <Card className="bg-slate-800/50 border-slate-700/50 shadow-xl flex-1 flex flex-col">
                        <CardHeader>
                          <CardTitle className="text-white flex items-center space-x-3 text-lg">
                            <MessageSquare className="w-6 h-6 text-purple-400" />
                            <span>Ações</span>
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4 flex-grow flex flex-col justify-center">
                          <Button
                            onClick={() => handleRepositoryAction('generate-post', selectedRepository)}
                            className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 h-12 text-base"
                          >
                            <Code className="w-5 h-5 mr-3" />
                            Gerar Post do LinkedIn
                          </Button>

                          <Button
                            variant="outline"
                            onClick={() => handleRepositoryAction('analyze', selectedRepository)}
                            className="w-full border-green-500/50 text-green-400 hover:bg-green-500/10 h-12 text-base"
                          >
                            <Activity className="w-5 h-5 mr-3" />
                            Analisar Atividade
                          </Button>

                          <Button
                            variant="outline"
                            onClick={() => handleRepositoryAction('clone', selectedRepository)}
                            className="w-full border-slate-500/50 text-slate-300 hover:bg-slate-500/10 h-12 text-base"
                          >
                            <GitBranch className="w-5 h-5 mr-3" />
                            Copiar URL Clone
                          </Button>
                        </CardContent>
                      </Card>

                      {/* Quick Links Card */}
                      <Card className="bg-slate-800/50 border-slate-700/50 shadow-xl flex-1 flex flex-col">
                        <CardHeader>
                          <CardTitle className="text-white flex items-center space-x-3 text-lg">
                            <ExternalLink className="w-6 h-6 text-blue-400" />
                            <span>Links Rápidos</span>
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4 flex-grow flex flex-col justify-center">
                          <Button
                            variant="outline"
                            onClick={() => window.open(selectedRepository.html_url, '_blank')}
                            className="w-full border-blue-500/50 text-blue-400 hover:bg-blue-500/10 h-12 text-base"
                          >
                            <ExternalLink className="w-5 h-5 mr-3" />
                            Ver no GitHub
                          </Button>

                          <Button
                            variant="outline"
                            onClick={() => window.open(`${selectedRepository.html_url}/commits`, '_blank')}
                            className="w-full border-yellow-500/50 text-yellow-400 hover:bg-yellow-500/10 h-12 text-base"
                          >
                            <Clock className="w-5 h-5 mr-3" />
                            Ver Commits
                          </Button>

                          <Button
                            variant="outline"
                            onClick={() => window.open(`${selectedRepository.html_url}/issues`, '_blank')}
                            className="w-full border-orange-500/50 text-orange-400 hover:bg-orange-500/10 h-12 text-base"
                          >
                            <AlertCircle className="w-5 h-5 mr-3" />
                            Ver Issues
                          </Button>
                        </CardContent>
                      </Card>
                    </div>
                  </div>

                  {/* Advanced Analytics Section */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Commit Activity */}
                    <Card className="group relative overflow-hidden bg-gradient-to-br from-slate-800/90 via-slate-800/50 to-slate-900/90 backdrop-blur-xl border border-slate-700/50 shadow-2xl hover:shadow-yellow-500/10 transition-all duration-500">
                      <div className="absolute inset-0 bg-gradient-to-br from-yellow-600/5 via-transparent to-orange-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                      <CardHeader className="relative">
                        <CardTitle className="text-white flex items-center justify-between text-lg">
                          <div className="flex items-center space-x-3">
                            <div className="relative">
                              <div className="absolute inset-0 bg-yellow-500/20 rounded-full blur-lg" />
                              <Clock className="relative w-6 h-6 text-yellow-400" />
                            </div>
                            <span className="bg-gradient-to-r from-yellow-400 to-orange-300 bg-clip-text text-transparent">
                              Atividade de Commits
                            </span>
                          </div>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => window.open(`${selectedRepository.html_url}/commits`, '_blank')}
                            className="border-yellow-500/50 text-yellow-400 hover:bg-yellow-500/10"
                          >
                            <ExternalLink className="w-3 h-3 mr-1" />
                            Ver Todos
                          </Button>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="relative space-y-4">
                        {/* Mock commit activity */}
                        <div className="space-y-3">
                          {[
                            { message: 'feat: implement repository management', time: '2 horas atrás', author: 'Você' },
                            { message: 'fix: resolve authentication issues', time: '1 dia atrás', author: 'Você' },
                            { message: 'docs: update README with new features', time: '3 dias atrás', author: 'Você' },
                          ].map((commit, index) => (
                            <div key={index} className="flex items-start space-x-3 p-3 bg-slate-700/30 rounded-lg border border-slate-600/20 hover:border-yellow-500/30 transition-all duration-300">
                              <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0" />
                              <div className="flex-1 min-w-0">
                                <p className="text-white text-sm font-medium truncate">{commit.message}</p>
                                <div className="flex items-center space-x-2 mt-1">
                                  <span className="text-slate-400 text-xs">{commit.author}</span>
                                  <span className="text-slate-500 text-xs">•</span>
                                  <span className="text-slate-400 text-xs">{commit.time}</span>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>

                        <div className="text-center pt-2">
                          <p className="text-slate-400 text-sm">
                            Dados simulados - Integração com GitHub API em desenvolvimento
                          </p>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Repository Insights */}
                    <Card className="group relative overflow-hidden bg-gradient-to-br from-slate-800/90 via-slate-800/50 to-slate-900/90 backdrop-blur-xl border border-slate-700/50 shadow-2xl hover:shadow-purple-500/10 transition-all duration-500">
                      <div className="absolute inset-0 bg-gradient-to-br from-purple-600/5 via-transparent to-pink-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                      <CardHeader className="relative">
                        <CardTitle className="text-white flex items-center space-x-3 text-lg">
                          <div className="relative">
                            <div className="absolute inset-0 bg-purple-500/20 rounded-full blur-lg" />
                            <TrendingUp className="relative w-6 h-6 text-purple-400" />
                          </div>
                          <span className="bg-gradient-to-r from-purple-400 to-pink-300 bg-clip-text text-transparent">
                            Insights do Repositório
                          </span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="relative space-y-4">
                        <div className="grid grid-cols-2 gap-3">
                          <div className="text-center p-3 bg-gradient-to-br from-slate-700/50 to-slate-800/50 rounded-lg border border-slate-600/30">
                            <Eye className="w-5 h-5 text-blue-400 mx-auto mb-1" />
                            <div className="text-lg font-bold text-white">--</div>
                            <div className="text-xs text-slate-400">Views</div>
                          </div>
                          <div className="text-center p-3 bg-gradient-to-br from-slate-700/50 to-slate-800/50 rounded-lg border border-slate-600/30">
                            <Download className="w-5 h-5 text-green-400 mx-auto mb-1" />
                            <div className="text-lg font-bold text-white">--</div>
                            <div className="text-xs text-slate-400">Clones</div>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                            <span className="text-slate-300 text-sm">Tamanho do repositório</span>
                            <span className="text-white font-medium text-sm">--</span>
                          </div>
                          <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                            <span className="text-slate-300 text-sm">Último push</span>
                            <span className="text-white font-medium text-sm">{formatDate(selectedRepository.updated_at)}</span>
                          </div>
                          <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                            <span className="text-slate-300 text-sm">Branch padrão</span>
                            <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/30">
                              main
                            </Badge>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Quick Actions Section */}
                  <Card className="group relative overflow-hidden bg-gradient-to-r from-slate-800/60 via-slate-800/40 to-slate-800/60 backdrop-blur-xl border border-slate-700/50 shadow-xl">
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-transparent to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                    <CardHeader className="relative">
                      <CardTitle className="text-white flex items-center space-x-3 text-lg">
                        <div className="relative">
                          <div className="absolute inset-0 bg-blue-500/20 rounded-full blur-lg" />
                          <Settings className="relative w-6 h-6 text-blue-400" />
                        </div>
                        <span className="bg-gradient-to-r from-blue-400 to-purple-300 bg-clip-text text-transparent">
                          Ações Rápidas para SaaS
                        </span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="relative">
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                        <Button
                          onClick={() => handleRepositoryAction('generate-post', selectedRepository)}
                          className="h-16 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 flex-col space-y-2 text-white shadow-lg hover:shadow-xl transition-all duration-300"
                        >
                          <MessageSquare className="w-6 h-6" />
                          <span className="text-sm">Gerar Post LinkedIn</span>
                        </Button>

                        <Button
                          variant="outline"
                          onClick={() => handleRepositoryAction('analyze', selectedRepository)}
                          className="h-16 border-green-500/50 text-green-400 hover:bg-green-500/10 flex-col space-y-2 transition-all duration-300"
                        >
                          <Activity className="w-6 h-6" />
                          <span className="text-sm">Analisar Atividade</span>
                        </Button>

                        <Button
                          variant="outline"
                          onClick={() => handleRepositoryAction('clone', selectedRepository)}
                          className="h-16 border-slate-500/50 text-slate-300 hover:bg-slate-500/10 flex-col space-y-2 transition-all duration-300"
                        >
                          <GitBranch className="w-6 h-6" />
                          <span className="text-sm">Copiar Clone URL</span>
                        </Button>

                        <Button
                          variant="outline"
                          onClick={() => handleRepositoryAction('share', selectedRepository)}
                          className="h-16 border-blue-500/50 text-blue-400 hover:bg-blue-500/10 flex-col space-y-2 transition-all duration-300"
                        >
                          <Share2 className="w-6 h-6" />
                          <span className="text-sm">Compartilhar</span>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  );
}
