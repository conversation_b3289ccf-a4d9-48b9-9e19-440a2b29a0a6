{"name": "backend", "type": "module", "version": "1.0.0", "main": "src/app.js", "scripts": {"start": "node src/app.js", "start:https": "node src/server.js", "dev": "nodemon src/app.js", "dev:https": "nodemon src/server.js", "start:prod": "NODE_ENV=production node src/server-production.js", "dev:prod": "NODE_ENV=production nodemon src/server-production.js", "start:letsencrypt": "NODE_ENV=production node src/server-production.js", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint src/ --ext .js", "lint:fix": "eslint src/ --ext .js --fix", "format": "prettier --write src/", "format:check": "prettier --check src/", "code:check": "npm run lint && npm run format:check", "code:fix": "npm run lint:fix && npm run format"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google/generative-ai": "^0.24.1", "@octokit/rest": "^22.0.0", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "csrf": "^3.1.0", "dotenv": "^17.2.0", "express": "^5.1.0", "express-jwt": "^8.5.1", "express-rate-limit": "^7.5.1", "express-session": "^1.18.2", "express-validator": "^7.2.1", "greenlock-express": "^4.0.3", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "octokit": "^5.0.3"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "nodemon": "^3.1.10", "prettier": "^3.6.2"}}