import express from 'express';
import cors from 'cors';
import helmet from 'helmet'; // Importa helmet para headers de segurança
import rateLimit from 'express-rate-limit'; // Importa rate limiting
import session from 'express-session'; // Importa express-session para CSRF
import dotenv from 'dotenv';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Rate limiting para proteção contra ataques de força bruta
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 100, // Máximo 100 requisições por IP por janela
  message: {
    error: 'Muitas requisições deste IP, tente novamente em 15 minutos',
  },
  standardHeaders: true, // Retorna rate limit info nos headers
  legacyHeaders: false,
});

// Rate limiting específico para autenticação (mais restritivo)
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 20, // Aumentado de 5 para 20 tentativas de login por IP por janela
  message: {
    error: 'Muitas tentativas de login, tente novamente em 15 minutos',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiting específico para logout (menos restritivo)
const logoutLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 50, // Máximo 50 tentativas de logout por IP por janela
  message: {
    error: 'Muitas tentativas de logout, tente novamente em 15 minutos',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiting para rotas GitHub (muito permissivo para desenvolvimento)
const githubLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 1000, // Muito permissivo para desenvolvimento
  message: {
    error: 'Muitas requisições para GitHub API, tente novamente em 15 minutos',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiting específico para rotas de detalhes (muito permissivo)
const githubDetailsLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 2000, // Muito permissivo para detalhes de repositórios
  message: {
    error: 'Muitas requisições para detalhes de repositórios, tente novamente em 15 minutos',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Middleware de segurança com helmet
app.use(
  helmet({
    // Configurações específicas para nossa aplicação
    contentSecurityPolicy: process.env.NODE_ENV === 'production' ? {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"], // Permite CSS inline para Tailwind
        scriptSrc: ["'self'", "'unsafe-eval'", "'unsafe-inline'"], // Necessário para Vite em desenvolvimento
        imgSrc: ["'self'", 'data:', 'https:'], // Permite imagens de HTTPS
        connectSrc: [
          "'self'",
          'https://api.github.com',
          'https://generativelanguage.googleapis.com',
        ], // APIs que usaremos
        fontSrc: ["'self'", 'https://fonts.gstatic.com'], // Fontes do Google
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
      },
    } : false, // Desabilitar CSP em desenvolvimento
    // Outras configurações de segurança
    hsts: {
      maxAge: 31536000, // 1 ano
      includeSubDomains: true,
      preload: true,
    },
    noSniff: true, // Previne MIME sniffing
    referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
  })
);

// Aplicar rate limiting global
app.use(limiter);

// Configurar sessões para CSRF
app.use(
  session({
    secret: process.env.SESSION_SECRET || 'code2post-session-secret',
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: process.env.NODE_ENV === 'production', // HTTPS em produção
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000, // 24 horas
      sameSite: 'strict',
    },
  })
);

// Configuração de CORS para múltiplas origens
const allowedOrigins = [
  'https://www.code2post.com',
  'https://code2post.com',
  'https://code2-post-ecwdmja4s-gabriel-camarates-projects.vercel.app',
  process.env.FRONTEND_URL || 'http://localhost:5173'
].filter(Boolean); // Remove valores undefined/null

// Middleware CORS melhorado para lidar com preflight requests
app.use(
  cors({
    origin: function (origin, callback) {
      // Permitir requisições sem origin (mobile apps, etc.)
      if (!origin) return callback(null, true);

      // Verificar se a origin está na lista permitida
      if (allowedOrigins.indexOf(origin) !== -1) {
        callback(null, true);
      } else {
        console.log('❌ CORS blocked origin:', origin);
        console.log('✅ Allowed origins:', allowedOrigins);
        callback(new Error('Not allowed by CORS'));
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type', 
      'Authorization', 
      'x-github-token',
      'X-Requested-With',
      'Accept',
      'Origin'
    ],
    exposedHeaders: ['Content-Length', 'X-Requested-With'],
    maxAge: 86400, // Cache preflight por 24 horas
  })
);

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Importar rotas
import authRoutes from './routes/auth.js';
import registerRoutes from './routes/register.js';
import loginRoutes from './routes/login.js';
import githubRoutes from './routes/github.js';
import geminiRoutes from './routes/geminiRoutes.js';
import githubConfigRoutes from './routes/githubConfig.js';

// Importar middleware CSRF
import {
  generateCSRFToken,
  validateCSRFToken,
  getCSRFToken,
} from './middleware/csrf.js';

// Routes
app.get('/', (req, res) => {
  res.json({
    message: 'Code2Post API is running!',
    version: '1.0.0',
    status: 'active',
  });
});

// Rota específica para logout com rate limiting menos restritivo (ANTES do CSRF)
app.post('/auth/logout', logoutLimiter, (req, res) => {
  res.json({ success: true, message: 'Logout realizado com sucesso' });
});

// Usar rota de registro (ANTES do CSRF)
app.use('/auth/register', authLimiter, registerRoutes);

// Usar rota de login (ANTES do CSRF)
app.use('/auth/login', authLimiter, loginRoutes);

// Usar rotas de autenticação com rate limiting específico e CSRF
app.use('/auth', authLimiter, validateCSRFToken, authRoutes);

// Usar rotas do GitHub
app.use('/auth/github', githubLimiter, githubRoutes);

// Usar rotas de configuração do GitHub (sem CSRF para desenvolvimento)
app.use('/api/github', githubDetailsLimiter, githubConfigRoutes);

// Usar rotas da Gemini API com CSRF
app.use('/api/gemini', validateCSRFToken, geminiRoutes);

// Rota para obter token CSRF
app.get('/csrf-token', generateCSRFToken, getCSRFToken);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Something went wrong!',
    message: err.message,
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl,
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Code2Post API running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
});

export default app;
