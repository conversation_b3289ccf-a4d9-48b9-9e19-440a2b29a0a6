import express from 'express';
import { Octokit } from 'octokit';
import jwt from 'jsonwebtoken';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// Armazenar tokens temporários (em produção, seria um banco de dados)
const tempTokens = new Map();

/**
 * Rota 1: Iniciar autenticação GitHub OAuth
 *
 * Esta rota redireciona o usuário para o GitHub para autorizar nossa aplicação.
 * O GitHub retornará um código temporário que usaremos para obter o access token.
 */
router.get('/login', (req, res) => {
  // Configuração do GitHub OAuth (dentro da função para garantir carregamento)
  const GITHUB_CLIENT_ID = process.env.GITHUB_CLIENT_ID;
  const GITHUB_CLIENT_SECRET = process.env.GITHUB_CLIENT_SECRET;
  const GITHUB_CALLBACK_URL =
    process.env.GITHUB_CALLBACK_URL ||
    'http://localhost:5173/auth/github/callback';

  // Debug: Log das variáveis de ambiente
  console.log('🔧 Configuração GitHub OAuth:');
  console.log(
    '📋 GITHUB_CLIENT_ID:',
    GITHUB_CLIENT_ID ? 'DEFINIDO' : 'UNDEFINED'
  );
  console.log(
    '📋 GITHUB_CLIENT_SECRET:',
    GITHUB_CLIENT_SECRET ? 'DEFINIDO' : 'UNDEFINED'
  );
  console.log('📋 GITHUB_CALLBACK_URL:', GITHUB_CALLBACK_URL);

  // Gerar state aleatório para prevenir CSRF
  const state = Math.random().toString(36).substring(7);

  // Armazenar state temporariamente (em produção, seria em sessão)
  tempTokens.set(state, { timestamp: Date.now() });

  // Construir URL de autorização do GitHub
  const authUrl =
    `https://github.com/login/oauth/authorize?` +
    `client_id=${GITHUB_CLIENT_ID}&` +
    `redirect_uri=${encodeURIComponent(GITHUB_CALLBACK_URL)}&` +
    `scope=repo,user&` +
    `state=${state}`;

  console.log('🔐 Iniciando autenticação GitHub OAuth...');
  console.log('📋 Scopes solicitados: repo, user');
  console.log('🔄 State gerado:', state);
  console.log('🌐 URL de autorização:', authUrl);

  res.json({
    authUrl,
    state,
    message: 'Redirecione o usuário para esta URL para autorizar o GitHub',
  });
});

/**
 * Rota 1.1: Mostrar URL de autorização formatada (para testes)
 *
 * Esta rota mostra a URL de autorização de forma mais clara para facilitar os testes.
 */
router.get('/login-url', (req, res) => {
  // Gerar state aleatório para prevenir CSRF
  const state = Math.random().toString(36).substring(7);

  // Armazenar state temporariamente
  tempTokens.set(state, { timestamp: Date.now() });

  // Construir URL de autorização do GitHub
  const authUrl =
    `https://github.com/login/oauth/authorize?` +
    `client_id=${GITHUB_CLIENT_ID}&` +
    `redirect_uri=${encodeURIComponent(GITHUB_CALLBACK_URL)}&` +
    `scope=repo,user&` +
    `state=${state}`;

  console.log('🔐 URL de autorização gerada para testes...');
  console.log('🌐 URL completa:', authUrl);

  res.json({
    success: true,
    message: 'URL de autorização GitHub gerada com sucesso',
    data: {
      authUrl,
      state,
      instructions: [
        '1. Copie a URL abaixo',
        '2. Cole no seu navegador',
        '3. Autorize a aplicação Code2Post',
        '4. Você será redirecionado de volta',
      ],
    },
  });
});

/**
 * Rota 2: Callback do GitHub OAuth
 *
 * Esta rota é chamada pelo GitHub após o usuário autorizar nossa aplicação.
 * Recebemos um código temporário que trocamos por um access token.
 */
router.get('/callback', async (req, res) => {
  // Configuração do GitHub OAuth (dentro da função para garantir carregamento)
  const GITHUB_CLIENT_ID = process.env.GITHUB_CLIENT_ID;
  const GITHUB_CLIENT_SECRET = process.env.GITHUB_CLIENT_SECRET;
  const GITHUB_CALLBACK_URL =
    process.env.GITHUB_CALLBACK_URL ||
    'http://localhost:5173/auth/github/callback';

  const { code, state } = req.query;

  console.log('🔄 Callback recebido do GitHub');
  console.log('📝 Code:', code ? 'Recebido' : 'Não recebido');
  console.log('🔒 State:', state);
  console.log('🔧 Configuração no callback:');
  console.log(
    '📋 GITHUB_CLIENT_ID:',
    GITHUB_CLIENT_ID ? 'DEFINIDO' : 'UNDEFINED'
  );
  console.log(
    '📋 GITHUB_CLIENT_SECRET:',
    GITHUB_CLIENT_SECRET ? 'DEFINIDO' : 'UNDEFINED'
  );

  // Validar se recebemos o código
  if (!code) {
    return res.status(400).json({
      error: 'Código de autorização não recebido do GitHub',
      details: 'O usuário pode ter cancelado a autorização',
    });
  }

  // Validar state para prevenir CSRF
  if (!state || !tempTokens.has(state)) {
    return res.status(400).json({
      error: 'State inválido ou expirado',
      details: 'Possível tentativa de CSRF',
    });
  }

  // Verificar se este código já foi usado (melhorada)
  const stateData = tempTokens.get(state);
  if (stateData && stateData.usedCodes && stateData.usedCodes.includes(code)) {
    console.log('⚠️ Código já utilizado, retornando erro');
    return res.status(400).json({
      error: 'Código já utilizado',
      details: 'Este código de autorização já foi processado',
    });
  }

  // Marcar código como usado IMEDIATAMENTE para evitar reprocessamento
  if (stateData) {
    if (!stateData.usedCodes) {
      stateData.usedCodes = [];
    }
    stateData.usedCodes.push(code);
    tempTokens.set(state, stateData);
  }

  try {
    // Trocar código por access token
    console.log('🔄 Trocando código por access token...');

    const tokenResponse = await fetch(
      'https://github.com/login/oauth/access_token',
      {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          client_id: GITHUB_CLIENT_ID,
          client_secret: GITHUB_CLIENT_SECRET,
          code,
          redirect_uri: GITHUB_CALLBACK_URL,
        }),
      }
    );

    const tokenData = await tokenResponse.json();

    if (tokenData.error) {
      throw new Error(
        `GitHub OAuth Error: ${tokenData.error_description || tokenData.error}`
      );
    }

    console.log('✅ Access token obtido com sucesso');
    console.log('📋 Scopes:', tokenData.scope);
    console.log(
      '🔑 Access Token:',
      `${tokenData.access_token.substring(0, 10)}...`
    );

    // Criar instância do Octokit com o token
    const octokit = new Octokit({
      auth: tokenData.access_token,
    });

    // Buscar informações do usuário
    const { data: user } = await octokit.rest.users.getAuthenticated();

    console.log('👤 Usuário GitHub:', user.login);
    console.log('📧 Email:', user.email);

    // Gerar token JWT para autenticação
    const accessToken = jwt.sign(
      {
        userId: user.id.toString(),
        email: user.email,
        name: user.name,
        githubId: user.id,
        githubUsername: user.login,
      },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    // Retornar dados do usuário e tokens
    res.json({
      success: true,
      message: 'Autenticação GitHub realizada com sucesso',
      user: {
        id: user.id,
        login: user.login,
        name: user.name,
        email: user.email,
        avatar_url: user.avatar_url,
        github_token: tokenData.access_token,
        scope: tokenData.scope,
      },
      accessToken: accessToken,  // Token JWT para autenticação
      githubToken: tokenData.access_token,  // Token GitHub para API
    });

    // Limpar token temporário APÓS enviar a resposta
    setTimeout(() => {
      tempTokens.delete(state);
    }, 10000); // Aguardar 10 segundos para permitir múltiplas chamadas
  } catch (error) {
    console.error('❌ Erro na autenticação GitHub:', error.message);

    // Limpar token temporário em caso de erro
    tempTokens.delete(state);

    res.status(500).json({
      error: 'Erro na autenticação GitHub',
      details: error.message,
    });
  }
});

/**
 * Rota 3: Buscar repositórios do usuário
 *
 * Esta rota usa o Octokit para buscar todos os repositórios do usuário autenticado.
 * Inclui repositórios próprios e de organizações (se autorizado).
 */
router.get('/repositories', authenticateToken, async (req, res) => {
  try {
    // Em produção, buscaríamos o token do GitHub do banco de dados
    // Por enquanto, vamos simular com um token mockado
    const githubToken = req.headers['x-github-token'];

    if (!githubToken) {
      return res.status(401).json({
        error: 'Token do GitHub não fornecido',
        details: 'Use o header x-github-token',
      });
    }

    const octokit = new Octokit({
      auth: githubToken,
    });

    console.log('🔍 Buscando repositórios do usuário...');

    // Buscar repositórios (próprios e de organizações)
    const { data: repos } = await octokit.rest.repos.listForAuthenticatedUser({
      sort: 'updated',
      per_page: 100,
    });

    console.log(`✅ ${repos.length} repositórios encontrados`);

    // Formatar dados dos repositórios
    const formattedRepos = repos.map(repo => ({
      id: repo.id,
      name: repo.name,
      full_name: repo.full_name,
      description: repo.description,
      private: repo.private,
      fork: repo.fork,
      language: repo.language,
      stargazers_count: repo.stargazers_count,
      forks_count: repo.forks_count,
      updated_at: repo.updated_at,
      html_url: repo.html_url,
      clone_url: repo.clone_url,
    }));

    res.json({
      success: true,
      repositories: formattedRepos,
      total: formattedRepos.length,
    });
  } catch (error) {
    console.error('❌ Erro ao buscar repositórios:', error.message);

    res.status(500).json({
      error: 'Erro ao buscar repositórios',
      details: error.message,
    });
  }
});

/**
 * Rota 4: Buscar commits recentes de um repositório
 *
 * Esta rota busca os commits mais recentes de um repositório específico.
 * Útil para detectar atividade recente e gerar posts.
 */
router.get(
  '/repositories/:owner/:repo/commits',
  authenticateToken,
  async (req, res) => {
    const { owner, repo } = req.params;
    const { per_page = 10, page = 1 } = req.query;

    try {
      const githubToken = req.headers['x-github-token'];

      if (!githubToken) {
        return res.status(401).json({
          error: 'Token do GitHub não fornecido',
        });
      }

      const octokit = new Octokit({
        auth: githubToken,
      });

      console.log(`🔍 Buscando commits do repositório ${owner}/${repo}...`);

      const { data: commits } = await octokit.rest.repos.listCommits({
        owner,
        repo,
        per_page: parseInt(per_page),
        page: parseInt(page),
      });

      console.log(`✅ ${commits.length} commits encontrados`);

      // Formatar dados dos commits
      const formattedCommits = commits.map(commit => ({
        sha: commit.sha,
        message: commit.commit.message,
        author: {
          name: commit.commit.author.name,
          email: commit.commit.author.email,
          date: commit.commit.author.date,
        },
        committer: {
          name: commit.commit.committer.name,
          email: commit.commit.committer.email,
          date: commit.commit.committer.date,
        },
        html_url: commit.html_url,
        parents: commit.parents.map(p => p.sha),
      }));

      res.json({
        success: true,
        repository: `${owner}/${repo}`,
        commits: formattedCommits,
        total: formattedCommits.length,
      });
    } catch (error) {
      console.error('❌ Erro ao buscar commits:', error.message);

      res.status(500).json({
        error: 'Erro ao buscar commits',
        details: error.message,
      });
    }
  }
);



/**
 * Rota 7: Verificar permissões do usuário
 *
 * Esta rota verifica se o usuário tem as permissões necessárias
 * para acessar os recursos do GitHub.
 */
router.get('/permissions', authenticateToken, async (req, res) => {
  try {
    const githubToken = req.headers['x-github-token'];

    if (!githubToken) {
      return res.status(401).json({
        error: 'Token do GitHub não fornecido',
      });
    }

    const octokit = new Octokit({
      auth: githubToken,
    });

    // Buscar informações do usuário autenticado
    const { data: user } = await octokit.rest.users.getAuthenticated();

    // Buscar scopes do token (se disponível)
    const { data: scopes } = await octokit.rest.users.getAuthenticated();

    res.json({
      success: true,
      user: {
        id: user.id,
        login: user.login,
        name: user.name,
        email: user.email,
        avatar_url: user.avatar_url,
      },
      permissions: {
        has_repo_access: true, // Assumindo que o token tem acesso a repositórios
        scopes: scopes || ['repo', 'user'],
      },
    });
  } catch (error) {
    console.error('❌ Erro ao verificar permissões:', error.message);

    res.status(500).json({
      error: 'Erro ao verificar permissões',
      details: error.message,
    });
  }
});

export default router;
