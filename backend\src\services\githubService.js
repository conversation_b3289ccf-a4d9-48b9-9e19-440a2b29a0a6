import { Octokit } from '@octokit/rest';

// Armazenamento temporário de tokens GitHub por usuário
// Em produção, isso deveria estar em um banco de dados
const userGitHubTokens = new Map();

/**
 * Armazena o token GitHub de um usuário
 * @param {string} userId - ID do usuário
 * @param {string} token - Token GitHub
 */
function storeGitHubToken(userId, token) {
  console.log(`💾 Armazenando token GitHub para userId: ${userId}`);
  userGitHubTokens.set(userId, token);
  console.log(`✅ Token armazenado. Total de tokens: ${userGitHubTokens.size}`);
}

/**
 * Obtém o token GitHub de um usuário
 * @param {string} userId - ID do usuário
 * @returns {string|null} Token GitHub ou null se não encontrado
 */
function getGitHubToken(userId) {
  const token = userGitHubTokens.get(userId) || null;
  console.log(
    `🔍 Buscando token GitHub para userId: ${userId}, encontrado: ${token ? 'SIM' : 'NÃO'}`
  );
  return token;
}

/**
 * Cria uma instância do Octokit para um usuário
 * @param {string} userId - ID do usuário
 * @returns {Octokit|null} Instância do Octokit ou null se não houver token
 */
function createOctokit(userId) {
  const token = getGitHubToken(userId);
  if (!token) {
    return null;
  }

  return new Octokit({
    auth: token,
    userAgent: 'Code2Post-App',
  });
}

/**
 * Busca repositórios de um usuário
 * @param {string} userId - ID do usuário
 * @returns {Promise<Array>} Lista de repositórios
 */
async function getRepositories(userId) {
  try {
    const octokit = createOctokit(userId);
    if (!octokit) {
      throw new Error('Token GitHub não encontrado');
    }

    const response = await octokit.repos.listForAuthenticatedUser({
      sort: 'updated',
      per_page: 30,
    });

    return response.data.map(repo => ({
      id: repo.id,
      name: repo.name,
      full_name: repo.full_name,
      description: repo.description,
      private: repo.private,
      html_url: repo.html_url,
      updated_at: repo.updated_at,
      language: repo.language,
      stargazers_count: repo.stargazers_count,
      forks_count: repo.forks_count,
    }));
  } catch (error) {
    console.error('Erro ao buscar repositórios:', error);
    throw new Error('Falha ao buscar repositórios do GitHub');
  }
}

/**
 * Busca commits de um repositório
 * @param {string} userId - ID do usuário
 * @param {string} repoName - Nome do repositório
 * @param {string} branch - Branch (padrão: main)
 * @param {number} limit - Limite de commits (padrão: 10)
 * @returns {Promise<Array>} Lista de commits
 */
async function getCommits(userId, repoName, branch = 'main', limit = 10) {
  try {
    const octokit = createOctokit(userId);
    if (!octokit) {
      throw new Error('Token GitHub não encontrado');
    }

    // Primeiro, vamos buscar o usuário para obter o username
    const userResponse = await octokit.users.getAuthenticated();
    const username = userResponse.data.login;

    const response = await octokit.repos.listCommits({
      owner: username,
      repo: repoName,
      sha: branch,
      per_page: Math.min(limit, 100), // GitHub limita a 100 por página
    });

    return response.data.map(commit => ({
      sha: commit.sha,
      commit: {
        message: commit.commit.message,
        author: {
          name: commit.commit.author.name,
          email: commit.commit.author.email,
          date: commit.commit.author.date,
        },
        committer: {
          name: commit.commit.committer.name,
          email: commit.commit.committer.email,
          date: commit.commit.committer.date,
        },
      },
      author: commit.author,
      committer: commit.committer,
      html_url: commit.html_url,
      parents: commit.parents,
    }));
  } catch (error) {
    console.error('Erro ao buscar commits:', error);
    throw new Error('Falha ao buscar commits do GitHub');
  }
}

/**
 * Busca informações de um repositório específico
 * @param {string} userId - ID do usuário
 * @param {string} repoName - Nome do repositório
 * @returns {Promise<Object>} Informações do repositório
 */
async function getRepository(userId, repoName) {
  try {
    const octokit = createOctokit(userId);
    if (!octokit) {
      throw new Error('Token GitHub não encontrado');
    }

    const userResponse = await octokit.users.getAuthenticated();
    const username = userResponse.data.login;

    const response = await octokit.repos.get({
      owner: username,
      repo: repoName,
    });

    return {
      id: response.data.id,
      name: response.data.name,
      full_name: response.data.full_name,
      description: response.data.description,
      private: response.data.private,
      html_url: response.data.html_url,
      updated_at: response.data.updated_at,
      language: response.data.language,
      stargazers_count: response.data.stargazers_count,
      forks_count: response.data.forks_count,
      open_issues_count: response.data.open_issues_count,
      default_branch: response.data.default_branch,
    };
  } catch (error) {
    console.error('Erro ao buscar repositório:', error);
    throw new Error('Falha ao buscar informações do repositório');
  }
}

/**
 * Verifica se um token GitHub é válido
 * @param {string} token - Token GitHub
 * @returns {Promise<boolean>} True se válido, false caso contrário
 */
async function validateGitHubToken(token) {
  try {
    const octokit = new Octokit({
      auth: token,
      userAgent: 'Code2Post-App',
    });

    const response = await octokit.users.getAuthenticated();
    return !!response.data;
  } catch (error) {
    console.error('Token GitHub inválido:', error);
    return false;
  }
}

export {
  storeGitHubToken,
  getGitHubToken,
  getRepositories,
  getCommits,
  getRepository,
  validateGitHubToken,
};
