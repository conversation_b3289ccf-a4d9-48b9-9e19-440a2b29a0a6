import api from './api';
import type { GitHubRepository, GitHubCommit, GitHubUser } from '@/types';

class GitHubService {
  // Obter token GitHub do localStorage
  private getGitHubToken(): string | null {
    return localStorage.getItem('githubToken');
  }

  // Buscar repositórios do usuário
  async getUserRepositories(): Promise<GitHubRepository[]> {
    const githubToken = this.getGitHubToken();
    
    if (!githubToken) {
      throw new Error('Token GitHub não encontrado. Faça login com GitHub primeiro.');
    }

    const response = await api.get<{ repositories: GitHubRepository[] }>('/api/github/repositories', {
      headers: {
        'x-github-token': githubToken
      }
    });
    
    return response.data.repositories || response.data;
  }

  // Buscar commits de um repositório
  async getRepositoryCommits(owner: string, repo: string, limit: number = 10): Promise<GitHubCommit[]> {
    const githubToken = this.getGitHubToken();
    
    if (!githubToken) {
      throw new Error('Token GitHub não encontrado. Faça login com GitHub primeiro.');
    }

    const response = await api.get<{ commits: GitHubCommit[] }>(`/api/github/repositories/${owner}/${repo}/commits`, {
      headers: {
        'x-github-token': githubToken
      },
      params: { limit }
    });
    
    return response.data.commits || response.data;
  }

  // Buscar informações do usuário
  async getUserInfo(): Promise<GitHubUser> {
    const githubToken = this.getGitHubToken();
    
    if (!githubToken) {
      throw new Error('Token GitHub não encontrado. Faça login com GitHub primeiro.');
    }

    const response = await api.get<GitHubUser>('/api/github/permissions', {
      headers: {
        'x-github-token': githubToken
      }
    });
    
    return response.data;
  }

  // Buscar commits recentes de todos os repositórios
  async getRecentCommits(limit: number = 20): Promise<GitHubCommit[]> {
    const githubToken = this.getGitHubToken();
    
    if (!githubToken) {
      throw new Error('Token GitHub não encontrado. Faça login com GitHub primeiro.');
    }

    const response = await api.get<GitHubCommit[]>('/api/github/commits', {
      headers: {
        'x-github-token': githubToken
      },
      params: { limit }
    });
    
    return response.data;
  }

  // Configurar webhook para um repositório
  async setupWebhook(owner: string, repo: string): Promise<void> {
    const githubToken = this.getGitHubToken();
    
    if (!githubToken) {
      throw new Error('Token GitHub não encontrado. Faça login com GitHub primeiro.');
    }

    await api.post(`/api/github/repositories/${owner}/${repo}/webhook`, {}, {
      headers: {
        'x-github-token': githubToken
      }
    });
  }

  // Remover webhook de um repositório
  async removeWebhook(owner: string, repo: string): Promise<void> {
    const githubToken = this.getGitHubToken();
    
    if (!githubToken) {
      throw new Error('Token GitHub não encontrado. Faça login com GitHub primeiro.');
    }

    await api.delete(`/api/github/repositories/${owner}/${repo}/webhook`, {
      headers: {
        'x-github-token': githubToken
      }
    });
  }

  // Buscar estatísticas do usuário
  async getUserStats(): Promise<{
    totalRepos: number;
    totalCommits: number;
    totalStars: number;
    languages: Record<string, number>;
  }> {
    const githubToken = this.getGitHubToken();
    
    if (!githubToken) {
      throw new Error('Token GitHub não encontrado. Faça login com GitHub primeiro.');
    }

    const response = await api.get('/api/github/stats', {
      headers: {
        'x-github-token': githubToken
      }
    });
    
    return response.data;
  }

  // Buscar linguagens de um repositório específico
  async getRepositoryLanguages(owner: string, repo: string): Promise<{
    name: string;
    bytes: number;
    percentage: string;
  }[]> {
    const githubToken = this.getGitHubToken();

    if (!githubToken) {
      throw new Error('Token GitHub não encontrado. Faça login com GitHub primeiro.');
    }

    const response = await api.get(`/api/github/repositories/${owner}/${repo}/languages`, {
      headers: {
        'x-github-token': githubToken
      }
    });

    return response.data.languages;
  }

  // Buscar total de commits de um repositório específico
  async getRepositoryCommitsCount(owner: string, repo: string): Promise<number> {
    const githubToken = this.getGitHubToken();

    if (!githubToken) {
      throw new Error('Token GitHub não encontrado. Faça login com GitHub primeiro.');
    }

    const response = await api.get(`/api/github/repositories/${owner}/${repo}/commits-count`, {
      headers: {
        'x-github-token': githubToken
      }
    });

    return response.data.totalCommits;
  }

  // Verificar se o usuário tem token GitHub configurado
  hasGitHubToken(): boolean {
    return !!this.getGitHubToken();
  }
}

export default new GitHubService(); 