// Tipos de autenticação
export interface User {
  id: string;
  name: string;
  email: string;
  avatar: string;
}

export interface AuthResponse {
  token: string;
  user: User;
}

// Tipos do GitHub
export interface GitHubRepository {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  private: boolean;
  fork: boolean;
  html_url: string;
  clone_url: string;
  stargazers_count: number;
  forks_count: number;
  language: string | null;
  updated_at: string;
  languages?: RepositoryLanguage[];
  totalCommits?: number;
}

export interface RepositoryLanguage {
  name: string;
  bytes: number;
  percentage: string;
}

export interface GitHubCommit {
  sha: string;
  commit: {
    message: string;
    author: {
      name: string;
      email: string;
      date: string;
    };
  };
  author: {
    login: string;
    avatar_url: string;
  } | null;
  html_url: string;
}

export interface GitHubUser {
  id: number;
  login: string;
  name: string;
  email: string;
  avatar_url: string;
  bio: string | null;
  public_repos: number;
  followers: number;
  following: number;
}

// Tipos de posts
export interface Post {
  id: string;
  title: string;
  content: string;
  hashtags: string[];
  platform: 'linkedin' | 'twitter' | 'medium';
  status: 'draft' | 'published' | 'scheduled';
  createdAt: string;
  publishedAt?: string;
  engagement?: {
    likes: number;
    comments: number;
    shares: number;
  };
}

export interface PostGenerationRequest {
  commitMessage: string;
  repositoryName: string;
  language: string;
  description?: string;
  tone?: 'professional' | 'casual' | 'technical' | 'enthusiastic';
  platform?: 'linkedin' | 'twitter' | 'medium';
}

// Tipos de configurações
export interface UserSettings {
  id: string;
  userId: string;
  defaultTone: 'professional' | 'casual' | 'technical' | 'enthusiastic';
  defaultPlatform: 'linkedin' | 'twitter' | 'medium';
  autoPublish: boolean;
  notificationPreferences: {
    email: boolean;
    push: boolean;
  };
  linkedInIntegration: {
    enabled: boolean;
    accessToken?: string;
  };
}

// Tipos de estatísticas
export interface UserStats {
  totalPosts: number;
  totalRepositories: number;
  totalCommits: number;
  averageEngagement: number;
  topLanguages: Array<{
    language: string;
    count: number;
  }>;
  monthlyActivity: Array<{
    month: string;
    posts: number;
    commits: number;
  }>;
}

// Tipos de notificações
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  read: boolean;
  createdAt: string;
}

// Tipos de API responses
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
} 